# Questionnaire Prompt Builder

## Overview

The Questionnaire Prompt Builder is a user-friendly interface that allows interviewers to create and customize AI interviewer prompts. This prompt will guide the AI agent during LiveKit interview sessions.

## Features

### ✨ Core Functionality
- **Create & Edit Prompts**: Rich textarea with syntax highlighting and character count
- **Auto-Save**: Prompts are automatically persisted using the API
- **Reset to Default**: Quick reset to a professional default prompt
- **Real-time Character Count**: Track prompt length as you type
- **Keyboard Shortcuts**: Save with Ctrl/Cmd + S

### 🎨 User Experience
- **Responsive Design**: Works on desktop and mobile devices
- **Dark Mode Support**: Automatically adapts to system preferences
- **Loading States**: Clear feedback during save operations
- **Error Handling**: User-friendly error messages
- **Navigation**: Integrated header with easy navigation between pages

### 📝 Prompt Writing Assistance
- **Example Prompts**: Built-in placeholder with professional examples
- **Writing Tips**: Helpful guidelines for creating effective interview prompts
- **Best Practices**: Suggestions for tone, structure, and content

## API Endpoints

### GET `/api/questionnaire-prompt-builder`
Returns the current prompt:
```json
{
  "prompt": "Your current interview prompt text..."
}
```

### POST `/api/questionnaire-prompt-builder`
Saves a new prompt:
```json
{
  "prompt": "Your new interview prompt text..."
}
```

Returns:
```json
{
  "success": true
}
```

## Usage

1. **Navigate to the Prompt Builder**: Visit `/questionnaire-prompt-builder`
2. **Edit Your Prompt**: Use the large textarea to craft your interview prompt
3. **Save Your Changes**: Click "Save Prompt" or use Ctrl/Cmd + S
4. **Start Interview**: Navigate to `/call` to begin the interview with your custom prompt

## Technical Implementation

- **Frontend**: Next.js 15 with React 19, TypeScript, and Tailwind CSS
- **Backend**: Next.js API routes with in-memory storage
- **Styling**: Tailwind CSS with custom design system
- **State Management**: React hooks for local state
- **Error Handling**: Comprehensive error boundaries and user feedback

## File Structure

```
src/
├── app/
│   ├── questionnaire-prompt-builder/
│   │   └── page.tsx                 # Main prompt builder UI
│   ├── api/
│   │   └── questionnaire-prompt-builder/
│   │       └── route.ts             # API endpoints
│   └── call/
│       └── page.tsx                 # Interview call page (placeholder)
├── components/
│   └── Header.tsx                   # Navigation header
└── globals.css                      # Global styles
```

## Future Enhancements

- **Prompt Templates**: Pre-built templates for different interview types
- **Prompt History**: Version control for prompt changes
- **Collaborative Editing**: Multiple users editing prompts
- **Prompt Testing**: Preview mode to test prompts before interviews
- **Export/Import**: Save prompts to files or share between teams
