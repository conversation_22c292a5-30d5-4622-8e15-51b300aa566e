"use client";

import { useState, useEffect } from "react";
import <PERSON> from "next/link";
import InterviewRoom<PERSON> from "./InterviewRoomUI";

// Mock participant data for preview
const mockParticipants = [
  {
    identity: "<PERSON> Doe-1234567890",
    isSpeaking: false,
  },
  {
    identity: "agent-interviewer",
    isSpeaking: true,
  },
];

export default function InterviewRoomPreview() {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [speakingParticipant, setSpeakingParticipant] = useState(0);

  // Update time every second for a more realistic feel
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Simulate speaking animation by switching who's speaking
  useEffect(() => {
    const speakingTimer = setInterval(() => {
      setSpeakingParticipant((prev) => (prev + 1) % mockParticipants.length);
    }, 3000);

    return () => clearInterval(speakingTimer);
  }, []);

  const participants = mockParticipants.map((participant, index) => ({
    ...participant,
    isSpeaking: index === speakingParticipant,
  }));

  return (
    <div className="min-h-[calc(100vh-80px)] bg-background py-6">
      <div className="max-w-4xl mx-auto p-6">
        {/* Preview Banner */}
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-1">
                🎭 Preview Mode
              </h3>
              <p className="text-blue-700 dark:text-blue-300 text-sm">
                This is a preview of what the interview room will look like. No
                actual connection is made.
              </p>
            </div>
            <Link
              href="/call"
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
            >
              Start Real Interview
            </Link>
          </div>
        </div>

        {/* Main Interview Room UI */}
        <InterviewRoomUI
          participants={participants}
          isPreview={true}
          previewTime={currentTime.toLocaleTimeString()}
          disconnectButton={
            <button
              disabled
              className="px-6 py-2 bg-red-400 text-white rounded-lg cursor-not-allowed opacity-60"
              title="This is preview mode - button is disabled"
            >
              End Interview (Preview)
            </button>
          }
        />

        {/* Additional Preview Information */}
        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <h3 className="font-semibold text-foreground mb-2">
            What happens in a real interview:
          </h3>
          <ul className="text-sm text-foreground/70 space-y-1">
            <li>• You&apos;ll connect to a LiveKit room with audio enabled</li>
            <li>
              • The AI interviewer will join automatically and start the
              conversation
            </li>
            <li>
              • Your microphone will be active for real-time communication
            </li>
            <li>
              • The interview will be guided by your custom prompt from the
              Prompt Builder
            </li>
            <li>
              • You can end the interview at any time using the disconnect
              button
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
