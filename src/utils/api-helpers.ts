// Types for API responses
export type ApiResponse<T = Record<string, unknown>> = {
  success: boolean;
  data?: T;
  error?: string;
};

/**
 * Utility function to handle API responses and extract error messages.
 * Provides consistent error handling across all API calls.
 *
 * @template T - The expected type of the response data
 * @param response - The fetch Response object to handle
 * @returns Promise resolving to a standardized ApiResponse object
 *
 * @example
 * ```typescript
 * const response = await fetch('/api/data');
 * const result = await handleApiResponse<{ message: string }>(response);
 * if (result.success) {
 *   console.log(result.data.message);
 * } else {
 *   console.error(result.error);
 * }
 * ```
 */
export async function handleApiResponse<T = Record<string, unknown>>(
  response: Response
): Promise<ApiResponse<T>> {
  if (response.ok) {
    const data = await response.json();
    return { success: true, data };
  } else {
    const errorData = await response.json().catch(() => ({}));
    const error = errorData.error || `Request failed (${response.status})`;
    return { success: false, error };
  }
}

/**
 * Utility function to handle network errors.
 * Provides consistent network error messaging and logging.
 *
 * @param error - The error object caught during network operations
 * @returns A user-friendly error message string
 *
 * @example
 * ```typescript
 * try {
 *   await fetch('/api/data');
 * } catch (error) {
 *   const message = handleNetworkError(error);
 *   setErrorMessage(message);
 * }
 * ```
 */
export function handleNetworkError(error: unknown): string {
  console.error("Network error:", error);
  return "Network error: Unable to connect to server";
}

/**
 * Generic API call wrapper that handles both HTTP and network errors.
 * Reduces boilerplate in components by providing consistent error handling.
 *
 * @template T - The expected type of the response data
 * @param url - The URL to make the request to
 * @param options - Optional fetch RequestInit options (method, headers, body, etc.)
 * @returns Promise resolving to a standardized ApiResponse object
 *
 * @example
 * ```typescript
 * // GET request
 * const result = await makeApiCall<{ prompt: string }>('/api/prompt');
 *
 * // POST request
 * const result = await makeApiCall('/api/save', {
 *   method: 'POST',
 *   headers: { 'Content-Type': 'application/json' },
 *   body: JSON.stringify({ data: 'value' })
 * });
 * ```
 */
export async function makeApiCall<T = Record<string, unknown>>(
  url: string,
  options?: RequestInit
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(url, options);
    return await handleApiResponse<T>(response);
  } catch (error) {
    return {
      success: false,
      error: handleNetworkError(error),
    };
  }
}

/**
 * Type definition for managing API call state in components.
 * Provides a standardized pattern for handling loading and error states.
 *
 * @example
 * ```typescript
 * const [apiState, setApiState] = useState<ApiCallState>({
 *   isLoading: false,
 *   error: null,
 *   setLoading: (loading) => setApiState(prev => ({ ...prev, isLoading: loading })),
 *   setError: (error) => setApiState(prev => ({ ...prev, error })),
 *   clearError: () => setApiState(prev => ({ ...prev, error: null }))
 * });
 * ```
 */
export type ApiCallState = {
  isLoading: boolean;
  error: string | null;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
};

/**
 * Collection of validation utilities for form inputs and API data.
 * Provides consistent validation logic across the application.
 */
export const validation = {
  /**
   * Validates participant name for the interview join form.
   *
   * @param name - The participant name to validate
   * @returns Object with validation result and optional error message
   *
   * @example
   * ```typescript
   * const result = validation.participantName('John Doe');
   * if (!result.isValid) {
   *   setError(result.error);
   * }
   * ```
   */
  participantName: (name: string): { isValid: boolean; error?: string } => {
    const trimmed = name.trim();
    if (!trimmed) {
      return { isValid: false, error: "Please enter your name" };
    }
    if (trimmed.length < 2) {
      return {
        isValid: false,
        error: "Name must be at least 2 characters long",
      };
    }
    if (trimmed.length > 50) {
      return { isValid: false, error: "Name must not exceed 50 characters" };
    }
    return { isValid: true };
  },

  /**
   * Validates interview prompt for the questionnaire builder.
   * Checks length constraints and provides UI feedback information.
   *
   * @param prompt - The prompt text to validate
   * @returns Object with validation result, UI state, and styling information
   *
   * @example
   * ```typescript
   * const result = validation.prompt(promptText);
   * if (result.canSave) {
   *   // Enable save button
   * }
   * // Apply character count styling
   * setCharacterCountClass(result.characterCountClass);
   * ```
   */
  prompt: (
    prompt: string
  ): {
    isValid: boolean;
    canSave: boolean;
    tooltip: string;
    characterCountClass: string;
    error?: string;
  } => {
    const MIN_LENGTH = 10;
    const MAX_LENGTH = 10000;
    const length = prompt.length;
    const trimmedLength = prompt.trim().length;

    const isValid = trimmedLength >= MIN_LENGTH && length <= MAX_LENGTH;
    const canSave = isValid && prompt.trim().length > 0;

    let tooltip = "Save prompt (Ctrl/Cmd + S)";
    let characterCountClass = "text-foreground/60";
    let error: string | undefined;

    if (length > MAX_LENGTH) {
      tooltip = `Prompt exceeds maximum length (${MAX_LENGTH} characters)`;
      characterCountClass = "text-red-600 font-medium";
      error = `Prompt must not exceed ${MAX_LENGTH} characters`;
    } else if (trimmedLength < MIN_LENGTH && trimmedLength > 0) {
      tooltip = `Prompt must be at least ${MIN_LENGTH} characters long`;
      characterCountClass = "text-foreground/60";
      error = `Prompt must be at least ${MIN_LENGTH} characters long`;
    } else if (length > MAX_LENGTH * 0.9) {
      characterCountClass = "text-yellow-600 font-medium";
    }

    return { isValid, canSave, tooltip, characterCountClass, error };
  },
};
