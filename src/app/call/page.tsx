"use client";

import { useState } from "react";
import Jo<PERSON><PERSON><PERSON> from "@/components/JoinForm";
import Interview<PERSON><PERSON> from "@/components/InterviewRoom";

interface RoomData {
  token: string;
  roomName: string;
  serverUrl: string;
  participantName: string;
}

export default function CallPage() {
  const [roomData, setRoomData] = useState<RoomData | null>(null);

  const handleJoinSuccess = (data: RoomData) => {
    setRoomData(data);
  };

  const handleDisconnect = () => {
    setRoomData(null);
  };

  return roomData ? (
    <InterviewRoom roomData={roomData} onDisconnect={handleDisconnect} />
  ) : (
    <JoinForm onJoinSuccess={handleJoinSuccess} />
  );
}
