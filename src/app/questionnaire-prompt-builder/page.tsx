"use client";

import { useState, useEffect } from "react";
import { makeApiCall, validation } from "@/utils/api-helpers";

export default function QuestionnairePromptBuilder() {
  const [prompt, setPrompt] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<"idle" | "success" | "error">(
    "idle"
  );
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [loadError, setLoadError] = useState<string>("");

  // Load existing prompt on component mount
  useEffect(() => {
    const loadPrompt = async () => {
      setLoadError("");

      const result = await makeApiCall("/api/questionnaire-prompt-builder");

      if (result.success) {
        setPrompt((result.data?.prompt as string) || "");
      } else {
        setLoadError(result.error || "Failed to load prompt");
        console.error("Failed to load prompt:", result.error);
      }

      setIsLoading(false);
    };

    loadPrompt();
  }, []);

  const handleSave = async () => {
    setIsSaving(true);
    setSaveStatus("idle");
    setErrorMessage("");

    const result = await makeApiCall("/api/questionnaire-prompt-builder", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ prompt }),
    });

    if (result.success) {
      setSaveStatus("success");
      console.log("Prompt saved successfully:", result.data?.message as string);
      setTimeout(() => setSaveStatus("idle"), 3000);
    } else {
      setErrorMessage(result.error || "Save failed");
      setSaveStatus("error");
      console.error("Failed to save prompt:", result.error);
    }

    setIsSaving(false);
  };

  // Retry function for failed saves
  const handleRetry = () => {
    const promptValidation = validation.prompt(prompt);
    if (promptValidation.canSave && !isSaving) {
      handleSave();
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.metaKey || e.ctrlKey) && e.key === "s") {
      e.preventDefault();
      const promptValidation = validation.prompt(prompt);
      if (promptValidation.canSave && !isSaving) {
        handleSave();
      }
    }
  };

  const handleReset = () => {
    setPrompt(
      `You are an AI interviewer conducting a professional interview. Your role is to:
- Ask thoughtful, relevant questions based on the candidate's responses
- Listen actively and ask follow-up questions when appropriate
- Maintain a friendly but professional tone throughout
- Keep the conversation natural and engaging
- Conclude the interview gracefully after covering key topics`
    );
    setSaveStatus("idle");
    setErrorMessage("");
  };

  if (isLoading) {
    return (
      <div className="min-h-[calc(100vh-80px)] bg-background flex items-center justify-center">
        <div className="text-foreground">Loading...</div>
      </div>
    );
  }

  // Show load error if prompt failed to load
  if (loadError) {
    return (
      <div className="min-h-[calc(100vh-80px)] bg-background p-6">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Questionnaire Prompt Builder
            </h1>
            <p className="text-foreground/70">
              Create and customize the AI interviewer prompt that will guide the
              interview conversation.
            </p>
          </div>

          <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-6 border border-red-200 dark:border-red-800">
            <div className="flex items-center gap-3 mb-4">
              <div className="text-red-600 dark:text-red-400">
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h2 className="text-lg font-semibold text-red-800 dark:text-red-200">
                Failed to Load Prompt
              </h2>
            </div>
            <p className="text-red-700 dark:text-red-300 mb-4">{loadError}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-[calc(100vh-80px)] bg-background p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Questionnaire Prompt Builder
          </h1>
          <p className="text-foreground/70">
            Create and customize the AI interviewer prompt that will guide the
            interview conversation.
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <label
                htmlFor="prompt"
                className="block text-sm font-medium text-foreground"
              >
                Interview Prompt
              </label>
              <span
                className={`text-sm ${
                  validation.prompt(prompt).characterCountClass
                }`}
              >
                {prompt.length} characters
                {prompt.length > 10000 && " (exceeds limit)"}
                {prompt.length > 9000 &&
                  prompt.length <= 10000 &&
                  " (approaching limit)"}
              </span>
            </div>
            <textarea
              id="prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Enter the prompt that will guide the AI interviewer...

Example:
You are an AI interviewer conducting a professional interview. Your role is to:
- Ask thoughtful, relevant questions based on the candidate's responses
- Listen actively and ask follow-up questions when appropriate
- Maintain a friendly but professional tone throughout
- Keep the conversation natural and engaging
- Conclude the interview gracefully after covering key topics"
              className="w-full h-64 p-4 border border-gray-300 dark:border-gray-600 rounded-lg
                         bg-white dark:bg-gray-700 text-foreground
                         focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                         resize-vertical min-h-[200px]"
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex gap-3">
              <button
                onClick={handleSave}
                disabled={isSaving || !validation.prompt(prompt).canSave}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400
                           text-white font-medium rounded-lg transition-colors
                           disabled:cursor-not-allowed"
                title={validation.prompt(prompt).tooltip}
              >
                {isSaving ? "Saving..." : "Save Prompt"}
              </button>

              <button
                onClick={handleReset}
                className="px-6 py-2 bg-gray-600 hover:bg-gray-700 
                           text-white font-medium rounded-lg transition-colors"
              >
                Reset to Default
              </button>
            </div>

            {saveStatus === "success" && (
              <div className="flex items-center gap-2 text-green-600 font-medium">
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                Prompt saved successfully!
              </div>
            )}

            {saveStatus === "error" && (
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2 text-red-600 font-medium">
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                  {errorMessage || "Failed to save prompt. Please try again."}
                </div>
                <button
                  onClick={handleRetry}
                  disabled={isSaving || !validation.prompt(prompt).canSave}
                  className="px-3 py-1 text-sm bg-red-600 hover:bg-red-700 disabled:bg-gray-400
                             text-white rounded transition-colors disabled:cursor-not-allowed"
                >
                  Retry
                </button>
              </div>
            )}
          </div>
        </div>

        <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
          <h2 className="text-lg font-semibold text-foreground mb-3">
            💡 Prompt Writing Tips
          </h2>
          <ul className="space-y-2 text-foreground/80">
            <li>• Be specific about the interview style and tone you want</li>
            <li>
              • Include instructions for how to handle different types of
              responses
            </li>
            <li>• Specify the types of questions the AI should ask</li>
            <li>• Consider including guidelines for follow-up questions</li>
            <li>• Think about how the AI should conclude the interview</li>
          </ul>
        </div>

        <div className="mt-6 text-center">
          <a
            href="/call"
            className="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 
                       text-white font-medium rounded-lg transition-colors"
          >
            Start Interview Call →
          </a>
        </div>
      </div>
    </div>
  );
}
