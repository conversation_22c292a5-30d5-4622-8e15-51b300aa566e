export default function Home() {
  return (
    <div className="min-h-[calc(100vh-80px)] bg-background flex items-center justify-center">
      <div className="max-w-2xl mx-auto text-center p-6">
        <h1 className="text-4xl font-bold text-foreground mb-6">
          Welcome to Interview Next Service
        </h1>
        <p className="text-foreground/70 mb-8 text-lg">
          Build custom interview prompts and conduct AI-powered interviews with
          LiveKit.
        </p>

        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/questionnaire-prompt-builder"
              className="inline-block px-8 py-3 bg-blue-600 hover:bg-blue-700
                         text-white font-medium rounded-lg transition-colors"
            >
              Build Interview Prompt
            </a>
            <a
              href="/preview"
              className="inline-block px-8 py-3 bg-gray-600 hover:bg-gray-700
                         text-white font-medium rounded-lg transition-colors"
            >
              Preview Interview Room
            </a>
          </div>

          <div className="text-foreground/50">
            <p>
              Create your interview prompt first, preview the room, then start a
              call.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
