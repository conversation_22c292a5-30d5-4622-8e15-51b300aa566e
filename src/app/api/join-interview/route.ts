import { NextRequest, NextResponse } from "next/server";
import { AccessToken } from "livekit-server-sdk";

// Do not cache endpoint result
export const revalidate = 0;

// Use a default room name that the agent can connect to
const DEFAULT_ROOM_NAME = "default-room";

export async function POST(request: NextRequest) {
  try {
    const { participantName } = await request.json();

    if (!participantName) {
      return NextResponse.json(
        { error: 'Missing "participantName" in request body' },
        { status: 400 }
      );
    }

    const apiKey = process.env.LIVEKIT_API_KEY;
    const apiSecret = process.env.LIVEKIT_API_SECRET;
    const livekitHost = process.env.LIVEKIT_URL;

    if (!apiKey || !apiSecret || !livekitHost) {
      return NextResponse.json(
        { error: "Server misconfigured" },
        { status: 500 }
      );
    }

    // Create access token for the participant to join the default room
    // Add timestamp to ensure unique identity
    const uniqueIdentity = `${participantName}-${Date.now()}`;
    const at = new AccessToken(apiKey, apiSecret, {
      identity: uniqueIdentity,
      ttl: "1h",
    });

    at.addGrant({
      room: DEFAULT_ROOM_NAME,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
      canPublishData: true,
      canUpdateOwnMetadata: true,
    });

    const token = await at.toJwt();

    return NextResponse.json(
      {
        token,
        roomName: DEFAULT_ROOM_NAME,
        serverUrl: livekitHost,
        participantName: uniqueIdentity,
      },
      { headers: { "Cache-Control": "no-store" } }
    );
  } catch (error) {
    console.error("Error creating token:", error);
    return NextResponse.json(
      { error: "Failed to join interview" },
      { status: 500 }
    );
  }
}
