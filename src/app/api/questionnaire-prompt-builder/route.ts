import { NextResponse } from "next/server";

// In-memory storage for simplicity (you could use filesystem or database)
let storedPrompt =
  "You are an AI interviewer. Conduct a professional interview by asking thoughtful questions and listening to responses. Keep the conversation natural and engaging.";

// Constants for validation
const MAX_PROMPT_LENGTH = 10000; // 10KB limit
const MIN_PROMPT_LENGTH = 10;

export async function GET() {
  try {
    return NextResponse.json({ prompt: storedPrompt });
  } catch (error) {
    console.error("Error retrieving prompt:", error);
    return NextResponse.json(
      { error: "Failed to retrieve prompt" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    // Validate Content-Type
    const contentType = request.headers.get("content-type");
    if (!contentType || !contentType.includes("application/json")) {
      return NextResponse.json(
        { error: "Content-Type must be application/json" },
        { status: 400 }
      );
    }

    // Parse and validate request body
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error("JSON parsing error:", parseError);
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      );
    }

    // Validate prompt field exists
    if (!body.hasOwnProperty("prompt")) {
      return NextResponse.json(
        { error: 'Missing required field "prompt"' },
        { status: 400 }
      );
    }

    const { prompt } = body;

    // Validate prompt type
    if (typeof prompt !== "string") {
      return NextResponse.json(
        { error: "Prompt must be a string" },
        { status: 400 }
      );
    }

    // Validate prompt content (basic sanitization) - check this first
    const trimmedPrompt = prompt.trim();
    if (!trimmedPrompt) {
      return NextResponse.json(
        { error: "Prompt cannot be empty or contain only whitespace" },
        { status: 400 }
      );
    }

    // Validate prompt length (use trimmed length)
    if (trimmedPrompt.length < MIN_PROMPT_LENGTH) {
      return NextResponse.json(
        {
          error: `Prompt must be at least ${MIN_PROMPT_LENGTH} characters long`,
        },
        { status: 400 }
      );
    }

    if (trimmedPrompt.length > MAX_PROMPT_LENGTH) {
      return NextResponse.json(
        { error: `Prompt must not exceed ${MAX_PROMPT_LENGTH} characters` },
        { status: 400 }
      );
    }

    // Store the prompt
    storedPrompt = trimmedPrompt;

    console.log(
      `Prompt updated successfully. Length: ${trimmedPrompt.length} characters`
    );

    return NextResponse.json({
      success: true,
      message: "Prompt saved successfully",
      promptLength: trimmedPrompt.length,
    });
  } catch (error) {
    console.error("Error saving prompt:", error);

    // Handle specific error types
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { error: "Invalid JSON format" },
        { status: 400 }
      );
    }

    if (error instanceof TypeError) {
      return NextResponse.json(
        { error: "Invalid request format" },
        { status: 400 }
      );
    }

    // Generic server error
    return NextResponse.json(
      { error: "Internal server error. Please try again later." },
      { status: 500 }
    );
  }
}
