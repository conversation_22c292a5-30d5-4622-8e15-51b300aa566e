import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import PreviewPage from "@/app/preview/page";

// Mock the InterviewRoomPreview component
jest.mock("@/components/InterviewRoomPreview", () => {
  return function MockInterviewRoomPreview() {
    return <div data-testid="interview-room-preview">Interview Room Preview</div>;
  };
});

describe("PreviewPage", () => {
  it("renders the InterviewRoomPreview component", () => {
    render(<PreviewPage />);

    expect(screen.getByTestId("interview-room-preview")).toBeInTheDocument();
    expect(screen.getByText("Interview Room Preview")).toBeInTheDocument();
  });
});
