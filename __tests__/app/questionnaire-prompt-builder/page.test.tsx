import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import QuestionnairePromptBuilderPage from "@/app/questionnaire-prompt-builder/page";

// Mock the QuestionnairePromptBuilder component
jest.mock("@/components/QuestionnairePromptBuilder", () => {
  return function MockQuestionnairePromptBuilder() {
    return <div data-testid="questionnaire-prompt-builder">Questionnaire Prompt Builder</div>;
  };
});

describe("QuestionnairePromptBuilderPage", () => {
  it("renders the QuestionnairePromptBuilder component", () => {
    render(<QuestionnairePromptBuilderPage />);

    expect(screen.getByTestId("questionnaire-prompt-builder")).toBeInTheDocument();
    expect(screen.getByText("Questionnaire Prompt Builder")).toBeInTheDocument();
  });
});
