import {
  handleApiResponse,
  handleNetworkError,
  makeApiCall,
  validation,
  type ApiResponse,
} from '@/utils/api-helpers'

// Mock fetch for testing
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>

describe('api-helpers', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Reset console.error mock
    jest.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('handleApiResponse', () => {
    it('should return success response for ok status', async () => {
      const mockData = { message: 'success' }
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue(mockData),
      } as unknown as Response

      const result = await handleApiResponse(mockResponse)

      expect(result).toEqual({
        success: true,
        data: mockData,
      })
    })

    it('should return error response for non-ok status', async () => {
      const mockError = { error: 'Something went wrong' }
      const mockResponse = {
        ok: false,
        status: 400,
        json: jest.fn().mockResolvedValue(mockError),
      } as unknown as Response

      const result = await handleApiResponse(mockResponse)

      expect(result).toEqual({
        success: false,
        error: 'Something went wrong',
      })
    })

    it('should handle JSON parsing errors gracefully', async () => {
      const mockResponse = {
        ok: false,
        status: 500,
        json: jest.fn().mockRejectedValue(new Error('Invalid JSON')),
      } as unknown as Response

      const result = await handleApiResponse(mockResponse)

      expect(result).toEqual({
        success: false,
        error: 'Request failed (500)',
      })
    })
  })

  describe('handleNetworkError', () => {
    it('should return network error message and log error', () => {
      const mockError = new Error('Network failure')
      const consoleSpy = jest.spyOn(console, 'error')

      const result = handleNetworkError(mockError)

      expect(result).toBe('Network error: Unable to connect to server')
      expect(consoleSpy).toHaveBeenCalledWith('Network error:', mockError)
    })
  })

  describe('makeApiCall', () => {
    it('should make successful API call', async () => {
      const mockData = { id: 1, name: 'test' }
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue(mockData),
      } as unknown as Response)

      const result = await makeApiCall('/api/test')

      expect(result).toEqual({
        success: true,
        data: mockData,
      })
      expect(mockFetch).toHaveBeenCalledWith('/api/test', undefined)
    })

    it('should handle network errors', async () => {
      const networkError = new Error('Network error')
      mockFetch.mockRejectedValueOnce(networkError)

      const result = await makeApiCall('/api/test')

      expect(result).toEqual({
        success: false,
        error: 'Network error: Unable to connect to server',
      })
    })

    it('should pass options to fetch', async () => {
      const mockData = { success: true }
      const options = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: 'data' }),
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue(mockData),
      } as unknown as Response)

      await makeApiCall('/api/test', options)

      expect(mockFetch).toHaveBeenCalledWith('/api/test', options)
    })
  })

  describe('validation', () => {
    describe('participantName', () => {
      it('should validate correct names', () => {
        expect(validation.participantName('John Doe')).toEqual({
          isValid: true,
        })
        expect(validation.participantName('A')).toEqual({
          isValid: false,
          error: 'Name must be at least 2 characters long',
        })
      })

      it('should reject empty or whitespace names', () => {
        expect(validation.participantName('')).toEqual({
          isValid: false,
          error: 'Please enter your name',
        })
        expect(validation.participantName('   ')).toEqual({
          isValid: false,
          error: 'Please enter your name',
        })
      })

      it('should reject names that are too short', () => {
        expect(validation.participantName('A')).toEqual({
          isValid: false,
          error: 'Name must be at least 2 characters long',
        })
      })

      it('should reject names that are too long', () => {
        const longName = 'A'.repeat(51)
        expect(validation.participantName(longName)).toEqual({
          isValid: false,
          error: 'Name must not exceed 50 characters',
        })
      })

      it('should trim whitespace before validation', () => {
        expect(validation.participantName('  John  ')).toEqual({
          isValid: true,
        })
      })
    })

    describe('prompt', () => {
      it('should validate correct prompts', () => {
        const validPrompt = 'This is a valid prompt that is long enough'
        const result = validation.prompt(validPrompt)

        expect(result.isValid).toBe(true)
        expect(result.canSave).toBe(true)
        expect(result.tooltip).toBe('Save prompt (Ctrl/Cmd + S)')
        expect(result.characterCountClass).toBe('text-foreground/60')
      })

      it('should reject prompts that are too short', () => {
        const shortPrompt = 'Short'
        const result = validation.prompt(shortPrompt)

        expect(result.isValid).toBe(false)
        expect(result.canSave).toBe(false)
        expect(result.error).toBe('Prompt must be at least 10 characters long')
      })

      it('should reject prompts that are too long', () => {
        const longPrompt = 'A'.repeat(10001)
        const result = validation.prompt(longPrompt)

        expect(result.isValid).toBe(false)
        expect(result.canSave).toBe(false)
        expect(result.error).toBe('Prompt must not exceed 10000 characters')
        expect(result.characterCountClass).toBe('text-red-600 font-medium')
      })

      it('should warn when approaching character limit', () => {
        const nearLimitPrompt = 'A'.repeat(9500)
        const result = validation.prompt(nearLimitPrompt)

        expect(result.isValid).toBe(true)
        expect(result.canSave).toBe(true)
        expect(result.characterCountClass).toBe('text-yellow-600 font-medium')
      })

      it('should handle empty prompts', () => {
        const result = validation.prompt('')

        expect(result.isValid).toBe(false)
        expect(result.canSave).toBe(false)
      })

      it('should handle whitespace-only prompts', () => {
        const result = validation.prompt('   ')

        expect(result.isValid).toBe(false)
        expect(result.canSave).toBe(false)
      })
    })
  })
})
