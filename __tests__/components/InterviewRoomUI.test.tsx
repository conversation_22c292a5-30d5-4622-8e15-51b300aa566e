import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import InterviewRoom<PERSON> from "@/components/InterviewRoomUI";

describe("InterviewRoomUI", () => {
  const mockParticipants = [
    {
      identity: "John Doe-123",
      isSpeaking: false,
    },
    {
      identity: "agent-interviewer",
      isSpeaking: true,
    },
  ];

  const mockDisconnectButton = <button>End Interview</button>;

  it("renders the interview room UI correctly", () => {
    render(
      <InterviewRoomUI
        participants={mockParticipants}
        disconnectButton={mockDisconnectButton}
      />
    );

    expect(screen.getByText("Interview in Progress")).toBeInTheDocument();
    expect(
      screen.getByText("Connected with <PERSON> Interviewer (2 participants)")
    ).toBeInTheDocument();
    expect(
      screen.getByText("Make sure your microphone is enabled and speak clearly.")
    ).toBeInTheDocument();
    expect(screen.getByText("End Interview")).toBeInTheDocument();
  });

  it("shows waiting message when only one participant", () => {
    const singleParticipant = [mockParticipants[0]];
    
    render(
      <InterviewRoomUI
        participants={singleParticipant}
        disconnectButton={mockDisconnectButton}
      />
    );

    expect(
      screen.getByText("Waiting for AI Interviewer to join...")
    ).toBeInTheDocument();
  });

  it("displays participants correctly", () => {
    render(
      <InterviewRoomUI
        participants={mockParticipants}
        disconnectButton={mockDisconnectButton}
      />
    );

    // Check user participant
    expect(screen.getByText("John Doe-123")).toBeInTheDocument();
    expect(screen.getByText("🔇 Silent")).toBeInTheDocument();

    // Check AI participant
    expect(screen.getByText("AI Interviewer")).toBeInTheDocument();
    expect(screen.getByText("🎤 Speaking")).toBeInTheDocument();
  });

  it("renders preview mode correctly", () => {
    const previewTime = "12:00:00 PM";
    
    render(
      <InterviewRoomUI
        participants={mockParticipants}
        isPreview={true}
        previewTime={previewTime}
        disconnectButton={mockDisconnectButton}
      />
    );

    expect(screen.getByText(`Preview started at ${previewTime}`)).toBeInTheDocument();
    expect(screen.getByText("In preview mode, controls are disabled")).toBeInTheDocument();
  });

  it("shows LIVE indicator in preview mode for speaking participants", () => {
    render(
      <InterviewRoomUI
        participants={mockParticipants}
        isPreview={true}
        disconnectButton={mockDisconnectButton}
      />
    );

    expect(screen.getByText("● LIVE")).toBeInTheDocument();
  });

  it("renders additional content when provided", () => {
    const additionalContent = <div data-testid="additional-content">Extra content</div>;
    
    render(
      <InterviewRoomUI
        participants={mockParticipants}
        disconnectButton={mockDisconnectButton}
        additionalContent={additionalContent}
      />
    );

    expect(screen.getByTestId("additional-content")).toBeInTheDocument();
    expect(screen.getByText("Extra content")).toBeInTheDocument();
  });

  it("applies correct styling for agent vs user participants", () => {
    render(
      <InterviewRoomUI
        participants={mockParticipants}
        disconnectButton={mockDisconnectButton}
      />
    );

    // Both participants should be rendered
    expect(screen.getByText("John Doe-123")).toBeInTheDocument();
    expect(screen.getByText("AI Interviewer")).toBeInTheDocument();
  });

  it("handles empty participants array", () => {
    render(
      <InterviewRoomUI
        participants={[]}
        disconnectButton={mockDisconnectButton}
      />
    );

    expect(
      screen.getByText("Waiting for AI Interviewer to join...")
    ).toBeInTheDocument();
  });
});
