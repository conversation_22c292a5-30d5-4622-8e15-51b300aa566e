import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import Header from "@/components/Header";

describe("Header", () => {
  it("renders the header with correct title", () => {
    render(<Header />);

    expect(screen.getByText("Interview Next Service")).toBeInTheDocument();
  });

  it("renders navigation links", () => {
    render(<Header />);

    const homeLink = screen.getByText("Interview Next Service");
    expect(homeLink.closest("a")).toHaveAttribute("href", "/");

    const promptBuilderLink = screen.getByText("Prompt Builder");
    expect(promptBuilderLink.closest("a")).toHaveAttribute(
      "href",
      "/questionnaire-prompt-builder"
    );

    const callLink = screen.getByText("Start Call");
    expect(callLink.closest("a")).toHaveAttribute("href", "/call");
  });

  it("has correct styling classes", () => {
    render(<Header />);

    const header = screen.getByRole("banner");
    expect(header).toHaveClass(
      "bg-white",
      "dark:bg-gray-900",
      "border-b",
      "border-gray-200",
      "dark:border-gray-700"
    );
  });

  it("renders navigation with proper structure", () => {
    render(<Header />);

    const nav = screen.getByRole("navigation");
    expect(nav).toBeInTheDocument();

    // Check that all navigation items are present
    expect(screen.getByText("Interview Next Service")).toBeInTheDocument();
    expect(screen.getByText("Prompt Builder")).toBeInTheDocument();
    expect(screen.getByText("Start Call")).toBeInTheDocument();
  });
});
