import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";
import JoinForm from "@/components/JoinForm";

// Mock the api-helpers module
jest.mock("@/utils/api-helpers", () => ({
  makeApiCall: jest.fn(),
  validation: {
    participantName: jest.fn(),
  },
}));

const mockMakeApiCall = require("@/utils/api-helpers").makeApiCall;
const mockValidation = require("@/utils/api-helpers").validation;

describe("JoinForm", () => {
  const mockOnJoinSuccess = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Default validation to return valid
    mockValidation.participantName.mockReturnValue({ isValid: true });
  });

  it("renders the form correctly", () => {
    render(<JoinForm onJoinSuccess={mockOnJoinSuccess} />);

    expect(screen.getByText("Start Interview Call")).toBeInTheDocument();
    expect(screen.getByLabelText("Your Name")).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText("Enter your name for the interview")
    ).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: "Join Interview" })
    ).toBeInTheDocument();
    expect(screen.getByText("← Back to Prompt Builder")).toBeInTheDocument();
  });

  it("updates participant name on input change", async () => {
    const user = userEvent.setup();
    render(<JoinForm onJoinSuccess={mockOnJoinSuccess} />);

    const nameInput = screen.getByLabelText("Your Name");
    await user.type(nameInput, "John Doe");

    expect(nameInput).toHaveValue("John Doe");
  });

  it("disables join button when name is empty", () => {
    render(<JoinForm onJoinSuccess={mockOnJoinSuccess} />);

    const joinButton = screen.getByRole("button", { name: "Join Interview" });
    expect(joinButton).toBeDisabled();
  });

  it("enables join button when name is provided", async () => {
    const user = userEvent.setup();
    render(<JoinForm onJoinSuccess={mockOnJoinSuccess} />);

    const nameInput = screen.getByLabelText("Your Name");
    const joinButton = screen.getByRole("button", { name: "Join Interview" });

    await user.type(nameInput, "John Doe");
    expect(joinButton).toBeEnabled();
  });

  it("calls onJoinSuccess when API call succeeds", async () => {
    const user = userEvent.setup();
    const mockRoomData = {
      token: "mock-token",
      roomName: "test-room",
      serverUrl: "wss://test.livekit.cloud",
      participantName: "John Doe-123",
    };

    mockMakeApiCall.mockResolvedValueOnce({
      success: true,
      data: mockRoomData,
    });

    render(<JoinForm onJoinSuccess={mockOnJoinSuccess} />);

    const nameInput = screen.getByLabelText("Your Name");
    const joinButton = screen.getByRole("button", { name: "Join Interview" });

    await user.type(nameInput, "John Doe");
    await user.click(joinButton);

    await waitFor(() => {
      expect(mockOnJoinSuccess).toHaveBeenCalledWith(mockRoomData);
    });

    expect(mockMakeApiCall).toHaveBeenCalledWith("/api/join-interview", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ participantName: "John Doe" }),
    });
  });

  it("shows error message when API call fails", async () => {
    const user = userEvent.setup();
    mockMakeApiCall.mockResolvedValueOnce({
      success: false,
      error: "Server error",
    });

    render(<JoinForm onJoinSuccess={mockOnJoinSuccess} />);

    const nameInput = screen.getByLabelText("Your Name");
    const joinButton = screen.getByRole("button", { name: "Join Interview" });

    await user.type(nameInput, "John Doe");
    await user.click(joinButton);

    await waitFor(() => {
      expect(screen.getByText("Server error")).toBeInTheDocument();
    });

    expect(mockOnJoinSuccess).not.toHaveBeenCalled();
  });

  it("shows default error message when API call fails without error message", async () => {
    const user = userEvent.setup();
    mockMakeApiCall.mockResolvedValueOnce({
      success: false,
    });

    render(<JoinForm onJoinSuccess={mockOnJoinSuccess} />);

    const nameInput = screen.getByLabelText("Your Name");
    const joinButton = screen.getByRole("button", { name: "Join Interview" });

    await user.type(nameInput, "John Doe");
    await user.click(joinButton);

    await waitFor(() => {
      expect(
        screen.getByText("Failed to join interview. Please try again.")
      ).toBeInTheDocument();
    });
  });

  it("shows validation error when name is invalid", async () => {
    const user = userEvent.setup();
    mockValidation.participantName.mockReturnValue({
      isValid: false,
      error: "Name must be at least 2 characters long",
    });

    render(<JoinForm onJoinSuccess={mockOnJoinSuccess} />);

    const nameInput = screen.getByLabelText("Your Name");
    const joinButton = screen.getByRole("button", { name: "Join Interview" });

    await user.type(nameInput, "A");
    await user.click(joinButton);

    await waitFor(() => {
      expect(
        screen.getByText("Name must be at least 2 characters long")
      ).toBeInTheDocument();
    });

    expect(mockMakeApiCall).not.toHaveBeenCalled();
  });

  it("shows loading state during API call", async () => {
    const user = userEvent.setup();
    // Create a promise that we can control
    let resolvePromise: (value: any) => void;
    const apiPromise = new Promise((resolve) => {
      resolvePromise = resolve;
    });
    mockMakeApiCall.mockReturnValueOnce(apiPromise);

    render(<JoinForm onJoinSuccess={mockOnJoinSuccess} />);

    const nameInput = screen.getByLabelText("Your Name");
    const joinButton = screen.getByRole("button", { name: "Join Interview" });

    await user.type(nameInput, "John Doe");
    await user.click(joinButton);

    // Check loading state
    expect(screen.getByText("Joining Interview...")).toBeInTheDocument();
    expect(joinButton).toBeDisabled();

    // Resolve the promise
    resolvePromise!({ success: true, data: {} });

    await waitFor(() => {
      expect(screen.getByText("Join Interview")).toBeInTheDocument();
    });
  });

  it("allows joining with Enter key", async () => {
    const user = userEvent.setup();
    const mockRoomData = {
      token: "mock-token",
      roomName: "test-room",
      serverUrl: "wss://test.livekit.cloud",
      participantName: "John Doe-123",
    };

    mockMakeApiCall.mockResolvedValueOnce({
      success: true,
      data: mockRoomData,
    });

    render(<JoinForm onJoinSuccess={mockOnJoinSuccess} />);

    const nameInput = screen.getByLabelText("Your Name");
    await user.type(nameInput, "John Doe");
    await user.keyboard("{Enter}");

    await waitFor(() => {
      expect(mockOnJoinSuccess).toHaveBeenCalledWith(mockRoomData);
    });
  });

  it("trims whitespace from participant name", async () => {
    const user = userEvent.setup();
    mockMakeApiCall.mockResolvedValueOnce({
      success: true,
      data: {},
    });

    render(<JoinForm onJoinSuccess={mockOnJoinSuccess} />);

    const nameInput = screen.getByLabelText("Your Name");
    const joinButton = screen.getByRole("button", { name: "Join Interview" });

    await user.type(nameInput, "  John Doe  ");
    await user.click(joinButton);

    await waitFor(() => {
      expect(mockMakeApiCall).toHaveBeenCalledWith("/api/join-interview", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ participantName: "John Doe" }),
      });
    });
  });

  it("has correct navigation link", () => {
    render(<JoinForm onJoinSuccess={mockOnJoinSuccess} />);

    const backLink = screen.getByText("← Back to Prompt Builder");
    expect(backLink.closest("a")).toHaveAttribute(
      "href",
      "/questionnaire-prompt-builder"
    );
  });
});
