import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";
import QuestionnairePromptBuilder from "@/components/QuestionnairePromptBuilder";

// Mock the api-helpers module
jest.mock("@/utils/api-helpers", () => ({
  makeApiCall: jest.fn(),
  validation: {
    prompt: jest.fn(),
  },
}));

const mockMakeApiCall = require("@/utils/api-helpers").makeApiCall;
const mockValidation = require("@/utils/api-helpers").validation;

describe("QuestionnairePromptBuilder", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Default validation to return valid
    mockValidation.prompt.mockReturnValue({
      isValid: true,
      canSave: true,
      tooltip: "Save prompt (Ctrl/Cmd + S)",
      characterCountClass: "text-foreground/60",
    });
  });

  it("renders the page correctly", async () => {
    mockMakeApiCall.mockResolvedValueOnce({
      success: true,
      data: { prompt: "Test prompt" },
    });

    render(<QuestionnairePromptBuilder />);

    await waitFor(() => {
      expect(
        screen.getByText("Questionnaire Prompt Builder")
      ).toBeInTheDocument();
    });

    expect(
      screen.getByText(
        "Create and customize the AI interviewer prompt that will guide the interview conversation."
      )
    ).toBeInTheDocument();
    expect(screen.getByLabelText("Interview Prompt")).toBeInTheDocument();
    expect(screen.getByText("Save Prompt")).toBeInTheDocument();
    expect(screen.getByText("Reset to Default")).toBeInTheDocument();
    expect(screen.getByText("Start Interview Call →")).toBeInTheDocument();
  });

  it("shows loading state initially", () => {
    mockMakeApiCall.mockImplementation(() => new Promise(() => {})); // Never resolves

    render(<QuestionnairePromptBuilder />);

    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });

  it("loads existing prompt on mount", async () => {
    const existingPrompt = "This is an existing prompt";
    mockMakeApiCall.mockResolvedValueOnce({
      success: true,
      data: { prompt: existingPrompt },
    });

    render(<QuestionnairePromptBuilder />);

    await waitFor(() => {
      const textarea = screen.getByLabelText("Interview Prompt");
      expect(textarea).toHaveValue(existingPrompt);
    });
  });

  it("shows load error when prompt fails to load", async () => {
    mockMakeApiCall.mockResolvedValueOnce({
      success: false,
      error: "Failed to load prompt",
    });

    render(<QuestionnairePromptBuilder />);

    await waitFor(() => {
      expect(screen.getByText("Failed to Load Prompt")).toBeInTheDocument();
      expect(screen.getByText("Failed to load prompt")).toBeInTheDocument();
      expect(screen.getByText("Retry")).toBeInTheDocument();
    });
  });

  it("updates prompt text when typing", async () => {
    const user = userEvent.setup();
    mockMakeApiCall.mockResolvedValueOnce({
      success: true,
      data: { prompt: "" },
    });

    render(<QuestionnairePromptBuilder />);

    await waitFor(() => {
      expect(screen.getByLabelText("Interview Prompt")).toBeInTheDocument();
    });

    const textarea = screen.getByLabelText("Interview Prompt");
    await user.type(textarea, "New prompt text");

    expect(textarea).toHaveValue("New prompt text");
  });

  it("saves prompt successfully", async () => {
    const user = userEvent.setup();
    mockMakeApiCall
      .mockResolvedValueOnce({
        success: true,
        data: { prompt: "Initial prompt" },
      })
      .mockResolvedValueOnce({
        success: true,
        data: { message: "Prompt saved successfully" },
      });

    render(<QuestionnairePromptBuilder />);

    await waitFor(() => {
      expect(screen.getByLabelText("Interview Prompt")).toBeInTheDocument();
    });

    const textarea = screen.getByLabelText("Interview Prompt");
    const saveButton = screen.getByText("Save Prompt");

    await user.clear(textarea);
    await user.type(textarea, "Updated prompt text");
    await user.click(saveButton);

    await waitFor(() => {
      expect(
        screen.getByText("Prompt saved successfully!")
      ).toBeInTheDocument();
    });

    expect(mockMakeApiCall).toHaveBeenCalledWith(
      "/api/questionnaire-prompt-builder",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ prompt: "Updated prompt text" }),
      }
    );
  });

  it("shows error when save fails", async () => {
    const user = userEvent.setup();
    mockMakeApiCall
      .mockResolvedValueOnce({
        success: true,
        data: { prompt: "Initial prompt" },
      })
      .mockResolvedValueOnce({
        success: false,
        error: "Save failed",
      });

    render(<QuestionnairePromptBuilder />);

    await waitFor(() => {
      expect(screen.getByLabelText("Interview Prompt")).toBeInTheDocument();
    });

    const saveButton = screen.getByText("Save Prompt");
    await user.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText("Save failed")).toBeInTheDocument();
      expect(screen.getByText("Retry")).toBeInTheDocument();
    });
  });

  it("disables save button when validation fails", async () => {
    mockMakeApiCall.mockResolvedValueOnce({
      success: true,
      data: { prompt: "" },
    });

    mockValidation.prompt.mockReturnValue({
      isValid: false,
      canSave: false,
      tooltip: "Prompt is too short",
      characterCountClass: "text-red-600",
    });

    render(<QuestionnairePromptBuilder />);

    await waitFor(() => {
      const saveButton = screen.getByText("Save Prompt");
      expect(saveButton).toBeDisabled();
    });
  });

  it("shows character count", async () => {
    const promptText = "This is a test prompt";
    mockMakeApiCall.mockResolvedValueOnce({
      success: true,
      data: { prompt: promptText },
    });

    render(<QuestionnairePromptBuilder />);

    await waitFor(() => {
      expect(
        screen.getByText(`${promptText.length} characters`)
      ).toBeInTheDocument();
    });
  });

  it("resets to default prompt", async () => {
    const user = userEvent.setup();
    mockMakeApiCall.mockResolvedValueOnce({
      success: true,
      data: { prompt: "Custom prompt" },
    });

    render(<QuestionnairePromptBuilder />);

    await waitFor(() => {
      expect(screen.getByLabelText("Interview Prompt")).toBeInTheDocument();
    });

    const resetButton = screen.getByText("Reset to Default");
    await user.click(resetButton);

    const textarea = screen.getByLabelText("Interview Prompt");
    expect(textarea.value).toContain(
      "You are an AI interviewer conducting a professional interview"
    );
  });

  it("handles keyboard shortcut for save", async () => {
    const user = userEvent.setup();
    mockMakeApiCall
      .mockResolvedValueOnce({
        success: true,
        data: { prompt: "Test prompt" },
      })
      .mockResolvedValueOnce({
        success: true,
        data: { message: "Prompt saved successfully" },
      });

    render(<QuestionnairePromptBuilder />);

    await waitFor(() => {
      expect(screen.getByLabelText("Interview Prompt")).toBeInTheDocument();
    });

    const textarea = screen.getByLabelText("Interview Prompt");
    textarea.focus();
    await user.keyboard("{Control>}s{/Control}");

    await waitFor(() => {
      expect(mockMakeApiCall).toHaveBeenCalledWith(
        "/api/questionnaire-prompt-builder",
        expect.any(Object)
      );
    });
  });

  it("shows loading state during save", async () => {
    const user = userEvent.setup();
    mockMakeApiCall
      .mockResolvedValueOnce({
        success: true,
        data: { prompt: "Test prompt" },
      })
      .mockImplementation(() => new Promise(() => {})); // Never resolves

    render(<QuestionnairePromptBuilder />);

    await waitFor(() => {
      expect(screen.getByLabelText("Interview Prompt")).toBeInTheDocument();
    });

    const saveButton = screen.getByText("Save Prompt");
    await user.click(saveButton);

    expect(screen.getByText("Saving...")).toBeInTheDocument();
    expect(saveButton).toBeDisabled();
  });

  it("retries save when retry button is clicked", async () => {
    const user = userEvent.setup();
    mockMakeApiCall
      .mockResolvedValueOnce({
        success: true,
        data: { prompt: "Test prompt" },
      })
      .mockResolvedValueOnce({
        success: false,
        error: "Save failed",
      })
      .mockResolvedValueOnce({
        success: true,
        data: { message: "Prompt saved successfully" },
      });

    render(<QuestionnairePromptBuilder />);

    await waitFor(() => {
      expect(screen.getByLabelText("Interview Prompt")).toBeInTheDocument();
    });

    const saveButton = screen.getByText("Save Prompt");
    await user.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText("Save failed")).toBeInTheDocument();
    });

    const retryButton = screen.getByText("Retry");
    await user.click(retryButton);

    await waitFor(() => {
      expect(
        screen.getByText("Prompt saved successfully!")
      ).toBeInTheDocument();
    });
  });

  it("has correct navigation links", async () => {
    mockMakeApiCall.mockResolvedValueOnce({
      success: true,
      data: { prompt: "Test prompt" },
    });

    render(<QuestionnairePromptBuilder />);

    await waitFor(() => {
      const callLink = screen.getByText("Start Interview Call →");
      expect(callLink.closest("a")).toHaveAttribute("href", "/call");
    });
  });

  it("shows prompt writing tips", async () => {
    mockMakeApiCall.mockResolvedValueOnce({
      success: true,
      data: { prompt: "Test prompt" },
    });

    render(<QuestionnairePromptBuilder />);

    await waitFor(() => {
      expect(screen.getByText("💡 Prompt Writing Tips")).toBeInTheDocument();
      expect(
        screen.getByText(
          "• Be specific about the interview style and tone you want"
        )
      ).toBeInTheDocument();
    });
  });
});
