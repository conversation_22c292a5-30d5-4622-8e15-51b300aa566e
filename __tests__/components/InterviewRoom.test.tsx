import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";
import InterviewRoom from "@/components/InterviewRoom";

// Mock LiveKit components
jest.mock("@livekit/components-react", () => ({
  LiveKitRoom: ({ children, onDisconnected }: any) => (
    <div data-testid="livekit-room" onClick={() => onDisconnected?.()}>
      {children}
    </div>
  ),
  RoomAudioRenderer: () => <div data-testid="room-audio-renderer" />,
  useParticipants: jest.fn(),
  DisconnectButton: ({ children, className, onClick }: any) => (
    <button
      className={className}
      onClick={onClick}
      data-testid="disconnect-button"
    >
      {children}
    </button>
  ),
}));

const mockUseParticipants =
  require("@livekit/components-react").useParticipants;

describe("InterviewRoom", () => {
  const mockRoomData = {
    token: "mock-token",
    roomName: "test-room",
    serverUrl: "wss://test.livekit.cloud",
    participantName: "John Doe-123",
  };

  const mockOnDisconnect = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Default to empty participants
    mockUseParticipants.mockReturnValue([]);
  });

  it("renders the interview room correctly", () => {
    render(
      <InterviewRoom roomData={mockRoomData} onDisconnect={mockOnDisconnect} />
    );

    expect(screen.getByText("Interview in Progress")).toBeInTheDocument();
    expect(
      screen.getByText("Waiting for AI Interviewer to join...")
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        "Make sure your microphone is enabled and speak clearly."
      )
    ).toBeInTheDocument();
    expect(screen.getByTestId("disconnect-button")).toBeInTheDocument();
    expect(screen.getByTestId("room-audio-renderer")).toBeInTheDocument();
  });

  it("shows correct message when only user is connected", () => {
    const userParticipant = {
      identity: "John Doe-123",
      isSpeaking: false,
    };
    mockUseParticipants.mockReturnValue([userParticipant]);

    render(
      <InterviewRoom roomData={mockRoomData} onDisconnect={mockOnDisconnect} />
    );

    expect(
      screen.getByText("Waiting for AI Interviewer to join...")
    ).toBeInTheDocument();
  });

  it("shows correct message when AI interviewer is connected", () => {
    const participants = [
      {
        identity: "John Doe-123",
        isSpeaking: false,
      },
      {
        identity: "agent-interviewer",
        isSpeaking: true,
      },
    ];
    mockUseParticipants.mockReturnValue(participants);

    render(
      <InterviewRoom roomData={mockRoomData} onDisconnect={mockOnDisconnect} />
    );

    expect(
      screen.getByText("Connected with AI Interviewer (2 participants)")
    ).toBeInTheDocument();
  });

  it("displays participants correctly", () => {
    const participants = [
      {
        identity: "John Doe-123",
        isSpeaking: false,
      },
      {
        identity: "agent-interviewer",
        isSpeaking: true,
      },
    ];
    mockUseParticipants.mockReturnValue(participants);

    render(
      <InterviewRoom roomData={mockRoomData} onDisconnect={mockOnDisconnect} />
    );

    // Check user participant
    expect(screen.getByText("John Doe-123")).toBeInTheDocument();
    expect(screen.getByText("🔇 Silent")).toBeInTheDocument();

    // Check AI participant
    expect(screen.getByText("AI Interviewer")).toBeInTheDocument();
    expect(screen.getByText("🎤 Speaking")).toBeInTheDocument();
  });

  it("shows speaking indicator correctly", () => {
    const participants = [
      {
        identity: "John Doe-123",
        isSpeaking: true,
      },
      {
        identity: "agent-interviewer",
        isSpeaking: false,
      },
    ];
    mockUseParticipants.mockReturnValue(participants);

    render(
      <InterviewRoom roomData={mockRoomData} onDisconnect={mockOnDisconnect} />
    );

    // User should show speaking
    const userSection = screen.getByText("John Doe-123").closest("div");
    expect(userSection).toHaveTextContent("🎤 Speaking");

    // AI should show silent
    const aiSection = screen.getByText("AI Interviewer").closest("div");
    expect(aiSection).toHaveTextContent("🔇 Silent");
  });

  it("identifies AI participants correctly", () => {
    const participants = [
      {
        identity: "regular-user",
        isSpeaking: false,
      },
      {
        identity: "agent-123",
        isSpeaking: false,
      },
      {
        identity: "interviewer-agent",
        isSpeaking: false,
      },
    ];
    mockUseParticipants.mockReturnValue(participants);

    render(
      <InterviewRoom roomData={mockRoomData} onDisconnect={mockOnDisconnect} />
    );

    // Regular user should show their identity
    expect(screen.getByText("regular-user")).toBeInTheDocument();

    // Both agent identities should show as "AI Interviewer"
    const aiInterviewers = screen.getAllByText("AI Interviewer");
    expect(aiInterviewers).toHaveLength(2);
  });

  it("calls onDisconnect when disconnect button is clicked", async () => {
    const user = userEvent.setup();
    render(
      <InterviewRoom roomData={mockRoomData} onDisconnect={mockOnDisconnect} />
    );

    const disconnectButton = screen.getByTestId("disconnect-button");
    await user.click(disconnectButton);

    expect(mockOnDisconnect).toHaveBeenCalledTimes(1);
  });

  it("passes correct props to LiveKitRoom", () => {
    render(
      <InterviewRoom roomData={mockRoomData} onDisconnect={mockOnDisconnect} />
    );

    const livekitRoom = screen.getByTestId("livekit-room");
    expect(livekitRoom).toBeInTheDocument();

    // The LiveKitRoom component should receive the correct props
    // We can't directly test props, but we can verify the component renders
    expect(livekitRoom).toBeInTheDocument();
  });

  it("handles multiple participants of same type", () => {
    const participants = [
      {
        identity: "user1",
        isSpeaking: false,
      },
      {
        identity: "user2",
        isSpeaking: true,
      },
      {
        identity: "agent-1",
        isSpeaking: false,
      },
      {
        identity: "agent-2",
        isSpeaking: true,
      },
    ];
    mockUseParticipants.mockReturnValue(participants);

    render(
      <InterviewRoom roomData={mockRoomData} onDisconnect={mockOnDisconnect} />
    );

    expect(
      screen.getByText("Connected with AI Interviewer (4 participants)")
    ).toBeInTheDocument();
    expect(screen.getByText("user1")).toBeInTheDocument();
    expect(screen.getByText("user2")).toBeInTheDocument();

    const aiInterviewers = screen.getAllByText("AI Interviewer");
    expect(aiInterviewers).toHaveLength(2);
  });

  it("shows correct visual indicators for participant types", () => {
    const participants = [
      {
        identity: "user1",
        isSpeaking: false,
      },
      {
        identity: "agent-1",
        isSpeaking: false,
      },
    ];
    mockUseParticipants.mockReturnValue(participants);

    render(
      <InterviewRoom roomData={mockRoomData} onDisconnect={mockOnDisconnect} />
    );

    // Check that participants are displayed
    expect(screen.getByText("user1")).toBeInTheDocument();
    expect(screen.getByText("AI Interviewer")).toBeInTheDocument();
  });

  it("renders audio renderer component", () => {
    render(
      <InterviewRoom roomData={mockRoomData} onDisconnect={mockOnDisconnect} />
    );

    expect(screen.getByTestId("room-audio-renderer")).toBeInTheDocument();
  });

  it("has correct styling classes on disconnect button", () => {
    render(
      <InterviewRoom roomData={mockRoomData} onDisconnect={mockOnDisconnect} />
    );

    const disconnectButton = screen.getByTestId("disconnect-button");
    expect(disconnectButton).toHaveClass(
      "px-6",
      "py-2",
      "bg-red-600",
      "hover:bg-red-700",
      "text-white",
      "rounded-lg",
      "transition-colors"
    );
  });
});
