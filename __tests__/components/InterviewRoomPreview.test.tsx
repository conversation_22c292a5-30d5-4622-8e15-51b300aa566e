import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import InterviewRoomPreview from "@/components/InterviewRoomPreview";

// Mock Next.js Link component
jest.mock("next/link", () => {
  return function MockLink({ children, href }: any) {
    return <a href={href}>{children}</a>;
  };
});

describe("InterviewRoomPreview", () => {
  beforeEach(() => {
    // Mock Date to have consistent time in tests
    jest.useFakeTimers();
    jest.setSystemTime(new Date("2024-01-01T12:00:00Z"));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it("renders preview banner with correct information", () => {
    render(<InterviewRoomPreview />);

    expect(screen.getByText("🎭 Preview Mode")).toBeInTheDocument();
    expect(
      screen.getByText(
        "This is a preview of what the interview room will look like. No actual connection is made."
      )
    ).toBeInTheDocument();
    expect(screen.getByText("Start Real Interview")).toBeInTheDocument();
  });

  it("displays interview room UI elements", () => {
    render(<InterviewRoomPreview />);

    expect(screen.getByText("Interview in Progress")).toBeInTheDocument();
    expect(
      screen.getByText("Connected with AI Interviewer (2 participants)")
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        "Make sure your microphone is enabled and speak clearly."
      )
    ).toBeInTheDocument();
  });

  it("shows mock participants", () => {
    render(<InterviewRoomPreview />);

    expect(screen.getByText("AI Interviewer")).toBeInTheDocument();
    expect(screen.getByText("John Doe-1234567890")).toBeInTheDocument();
  });

  it("displays disabled end interview button", () => {
    render(<InterviewRoomPreview />);

    const endButton = screen.getByText("End Interview (Preview)");
    expect(endButton).toBeInTheDocument();
    expect(endButton).toBeDisabled();
    expect(
      screen.getByText("In preview mode, controls are disabled")
    ).toBeInTheDocument();
  });

  it("shows informational content about real interviews", () => {
    render(<InterviewRoomPreview />);

    expect(
      screen.getByText("What happens in a real interview:")
    ).toBeInTheDocument();
    expect(
      screen.getByText("• You'll connect to a LiveKit room with audio enabled")
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        "• The AI interviewer will join automatically and start the conversation"
      )
    ).toBeInTheDocument();
  });

  it("includes link to start real interview", () => {
    render(<InterviewRoomPreview />);

    const startRealInterviewLink = screen.getByText("Start Real Interview");
    expect(startRealInterviewLink).toBeInTheDocument();
    expect(startRealInterviewLink.closest("a")).toHaveAttribute(
      "href",
      "/call"
    );
  });

  it("displays current time in preview", () => {
    render(<InterviewRoomPreview />);

    // Should show the mocked time
    expect(screen.getByText(/Preview started at/)).toBeInTheDocument();
  });
});
