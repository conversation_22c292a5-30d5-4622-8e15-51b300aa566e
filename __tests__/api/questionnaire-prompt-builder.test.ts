// Test the API validation logic
describe("/api/questionnaire-prompt-builder validation", () => {
  describe("prompt validation", () => {
    const validatePrompt = (prompt: any) => {
      // Replicate the validation logic from the API
      const MAX_PROMPT_LENGTH = 10000;
      const MIN_PROMPT_LENGTH = 10;

      if (typeof prompt !== "string") {
        return { valid: false, error: "Prompt must be a string" };
      }

      const trimmedPrompt = prompt.trim();
      if (!trimmedPrompt) {
        return {
          valid: false,
          error: "Prompt cannot be empty or contain only whitespace",
        };
      }

      if (trimmedPrompt.length < MIN_PROMPT_LENGTH) {
        return {
          valid: false,
          error: `Prompt must be at least ${MIN_PROMPT_LENGTH} characters long`,
        };
      }

      if (trimmedPrompt.length > MAX_PROMPT_LENGTH) {
        return {
          valid: false,
          error: `Prompt must not exceed ${MAX_PROMPT_LENGTH} characters`,
        };
      }

      return { valid: true, trimmedPrompt };
    };

    it("should validate correct prompts", () => {
      const validPrompt =
        "This is a valid interview prompt that is long enough";
      const result = validatePrompt(validPrompt);

      expect(result.valid).toBe(true);
      expect(result.trimmedPrompt).toBe(validPrompt);
    });

    it("should reject non-string prompts", () => {
      const result = validatePrompt(123);
      expect(result.valid).toBe(false);
      expect(result.error).toBe("Prompt must be a string");
    });

    it("should reject empty prompts", () => {
      const result = validatePrompt("");
      expect(result.valid).toBe(false);
      expect(result.error).toBe(
        "Prompt cannot be empty or contain only whitespace"
      );
    });

    it("should reject whitespace-only prompts", () => {
      const result = validatePrompt("   ");
      expect(result.valid).toBe(false);
      expect(result.error).toBe(
        "Prompt cannot be empty or contain only whitespace"
      );
    });

    it("should reject prompts that are too short", () => {
      const result = validatePrompt("short");
      expect(result.valid).toBe(false);
      expect(result.error).toBe("Prompt must be at least 10 characters long");
    });

    it("should reject prompts that are too long", () => {
      const longPrompt = "A".repeat(10001);
      const result = validatePrompt(longPrompt);
      expect(result.valid).toBe(false);
      expect(result.error).toBe("Prompt must not exceed 10000 characters");
    });

    it("should trim whitespace from prompts", () => {
      const promptWithWhitespace = "   Valid prompt that is long enough   ";
      const result = validatePrompt(promptWithWhitespace);
      expect(result.valid).toBe(true);
      expect(result.trimmedPrompt).toBe(promptWithWhitespace.trim());
    });
  });
});
