import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";

// Import the components
import QuestionnairePromptBuilder from "@/app/questionnaire-prompt-builder/page";
import Join<PERSON><PERSON> from "@/components/JoinForm";

// Mock LiveKit components for integration tests
jest.mock("@livekit/components-react", () => ({
  LiveKitRoom: ({ children }: any) => (
    <div data-testid="livekit-room">{children}</div>
  ),
  RoomAudioRenderer: () => <div data-testid="room-audio-renderer" />,
  useParticipants: () => [],
  DisconnectButton: ({ children }: any) => (
    <button data-testid="disconnect-button">{children}</button>
  ),
}));

// Mock livekit-server-sdk
jest.mock("livekit-server-sdk", () => ({
  AccessToken: jest.fn().mockImplementation(() => ({
    addGrant: jest.fn(),
    toJwt: jest.fn().mockResolvedValue("mock-jwt-token"),
  })),
}));

// Mock the api-helpers module
jest.mock("@/utils/api-helpers", () => ({
  makeApiCall: jest.fn(),
  validation: {
    prompt: jest.fn(),
    participantName: jest.fn(),
  },
}));

const mockMakeApiCall = require("@/utils/api-helpers").makeApiCall;
const mockValidation = require("@/utils/api-helpers").validation;

describe("Integration Tests - Complete User Flow", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset fetch mock
    global.fetch = jest.fn();
    // Default validation to return valid
    mockValidation.prompt.mockReturnValue({
      isValid: true,
      canSave: true,
      tooltip: "Save prompt (Ctrl/Cmd + S)",
      characterCountClass: "text-foreground/60",
    });
    mockValidation.participantName.mockReturnValue({ isValid: true });
  });

  describe("Prompt Creation and Persistence Flow", () => {
    it("should create, save, and retrieve a custom prompt", async () => {
      const user = userEvent.setup();
      const customPrompt =
        "This is a custom interview prompt for testing the complete flow";

      // Mock the API calls that the component will make
      // Mock initial load (GET)
      mockMakeApiCall.mockResolvedValueOnce({
        success: true,
        data: { prompt: "Default prompt" },
      });

      // Mock save (POST)
      mockMakeApiCall.mockResolvedValueOnce({
        success: true,
        data: {
          success: true,
          message: "Prompt saved successfully",
          promptLength: customPrompt.length,
        },
      });

      render(<QuestionnairePromptBuilder />);

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByLabelText("Interview Prompt")).toBeInTheDocument();
      });

      // Clear existing prompt and enter custom prompt
      const textarea = screen.getByLabelText("Interview Prompt");
      await user.clear(textarea);
      await user.type(textarea, customPrompt);

      // Save the prompt
      const saveButton = screen.getByText("Save Prompt");
      await user.click(saveButton);

      // Verify success message
      await waitFor(() => {
        expect(
          screen.getByText("Prompt saved successfully!")
        ).toBeInTheDocument();
      });

      // Verify the API was called correctly
      expect(mockMakeApiCall).toHaveBeenCalledWith(
        "/api/questionnaire-prompt-builder",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ prompt: customPrompt }),
        }
      );
    });

    it("should handle validation errors during prompt creation", async () => {
      const user = userEvent.setup();
      const shortPrompt = "Short";

      // Mock initial load
      mockMakeApiCall.mockResolvedValueOnce({
        success: true,
        data: { prompt: "" },
      });

      // Mock validation error response
      mockMakeApiCall.mockResolvedValueOnce({
        success: false,
        error: "Prompt must be at least 10 characters long",
      });

      render(<QuestionnairePromptBuilder />);

      await waitFor(() => {
        expect(screen.getByLabelText("Interview Prompt")).toBeInTheDocument();
      });

      const textarea = screen.getByLabelText("Interview Prompt");
      await user.type(textarea, shortPrompt);

      const saveButton = screen.getByText("Save Prompt");
      await user.click(saveButton);

      await waitFor(() => {
        expect(
          screen.getByText("Prompt must be at least 10 characters long")
        ).toBeInTheDocument();
      });
    });
  });

  describe("Interview Join Flow", () => {
    it("should successfully join an interview with valid participant name", async () => {
      const user = userEvent.setup();
      const participantName = "John Doe";
      const mockRoomData = {
        token: "mock-jwt-token",
        roomName: "default-room",
        serverUrl: "wss://test.livekit.cloud",
        participantName: "John Doe-123456789",
      };

      const mockOnJoinSuccess = jest.fn();

      // Mock the makeApiCall for JoinForm
      mockMakeApiCall.mockResolvedValueOnce({
        success: true,
        data: mockRoomData,
      });

      render(<JoinForm onJoinSuccess={mockOnJoinSuccess} />);

      // Enter participant name
      const nameInput = screen.getByLabelText("Your Name");
      await user.type(nameInput, participantName);

      // Join the interview
      const joinButton = screen.getByText("Join Interview");
      await user.click(joinButton);

      // Verify success callback was called
      await waitFor(() => {
        expect(mockOnJoinSuccess).toHaveBeenCalledWith(mockRoomData);
      });

      // Verify API was called correctly
      expect(mockMakeApiCall).toHaveBeenCalledWith("/api/join-interview", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ participantName }),
      });
    });

    it("should handle join errors gracefully", async () => {
      const user = userEvent.setup();
      const participantName = "John Doe";
      const mockOnJoinSuccess = jest.fn();

      // Mock the makeApiCall for JoinForm error
      mockMakeApiCall.mockResolvedValueOnce({
        success: false,
        error: "Server misconfigured",
      });

      render(<JoinForm onJoinSuccess={mockOnJoinSuccess} />);

      const nameInput = screen.getByLabelText("Your Name");
      await user.type(nameInput, participantName);

      const joinButton = screen.getByText("Join Interview");
      await user.click(joinButton);

      await waitFor(() => {
        expect(screen.getByText("Server misconfigured")).toBeInTheDocument();
      });

      expect(mockOnJoinSuccess).not.toHaveBeenCalled();
    });
  });

  describe("Error Handling Integration", () => {
    it("should handle network errors gracefully across the flow", async () => {
      const user = userEvent.setup();
      const mockOnJoinSuccess = jest.fn();

      // Mock network error
      mockMakeApiCall.mockResolvedValueOnce({
        success: false,
        error: "Network error: Unable to connect to server",
      });

      render(<JoinForm onJoinSuccess={mockOnJoinSuccess} />);

      const nameInput = screen.getByLabelText("Your Name");
      await user.type(nameInput, "John Doe");

      const joinButton = screen.getByText("Join Interview");
      await user.click(joinButton);

      await waitFor(() => {
        expect(
          screen.getByText("Network error: Unable to connect to server")
        ).toBeInTheDocument();
      });
    });

    it("should handle server errors during prompt operations", async () => {
      const user = userEvent.setup();

      // Mock initial load success
      mockMakeApiCall.mockResolvedValueOnce({
        success: true,
        data: { prompt: "Initial prompt" },
      });

      // Mock server error on save
      mockMakeApiCall.mockResolvedValueOnce({
        success: false,
        error: "Internal server error. Please try again later.",
      });

      render(<QuestionnairePromptBuilder />);

      await waitFor(() => {
        expect(screen.getByLabelText("Interview Prompt")).toBeInTheDocument();
      });

      const saveButton = screen.getByText("Save Prompt");
      await user.click(saveButton);

      await waitFor(() => {
        expect(
          screen.getByText("Internal server error. Please try again later.")
        ).toBeInTheDocument();
      });
    });
  });

  describe("User Experience Flow", () => {
    it("should provide smooth navigation between prompt creation and interview", async () => {
      // Mock prompt load
      mockMakeApiCall.mockResolvedValueOnce({
        success: true,
        data: { prompt: "Test prompt" },
      });

      render(<QuestionnairePromptBuilder />);

      await waitFor(() => {
        expect(screen.getByText("Start Interview Call →")).toBeInTheDocument();
      });

      // Verify navigation link is correct
      const callLink = screen.getByText("Start Interview Call →");
      expect(callLink.closest("a")).toHaveAttribute("href", "/call");
    });
  });
});
