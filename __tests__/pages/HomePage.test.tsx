import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import HomePage from "@/app/page";

describe("HomePage", () => {
  it("renders the main heading", () => {
    render(<HomePage />);

    expect(
      screen.getByText("Welcome to Interview Next Service")
    ).toBeInTheDocument();
  });

  it("renders the description", () => {
    render(<HomePage />);

    expect(
      screen.getByText(
        "Build custom interview prompts and conduct AI-powered interviews with LiveKit."
      )
    ).toBeInTheDocument();
  });

  it("renders the build interview prompt button", () => {
    render(<HomePage />);

    const promptButton = screen.getByText("Build Interview Prompt");
    expect(promptButton).toBeInTheDocument();
    expect(promptButton.closest("a")).toHaveAttribute(
      "href",
      "/questionnaire-prompt-builder"
    );
  });

  it("renders the helper text", () => {
    render(<HomePage />);

    expect(
      screen.getByText("Create your interview prompt first, then start a call.")
    ).toBeInTheDocument();
  });

  it("has correct page structure", () => {
    render(<HomePage />);

    // Check that the main container has proper styling
    const mainContainer = screen
      .getByText("Welcome to Interview Next Service")
      .closest("div");
    expect(mainContainer).toBeInTheDocument();
  });

  it("renders with proper accessibility", () => {
    render(<HomePage />);

    // Check that the main heading is properly structured
    const heading = screen.getByRole("heading", { level: 1 });
    expect(heading).toHaveTextContent("Welcome to Interview Next Service");
  });
});
