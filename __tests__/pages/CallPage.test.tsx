import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";
import CallPage from "@/app/call/page";

// Mock the components
jest.mock("@/components/JoinForm", () => {
  return function MockJoinForm({ onJoinSuccess }: any) {
    return (
      <div data-testid="join-form">
        <button
          onClick={() =>
            onJoinSuccess({
              token: "test-token",
              roomName: "test-room",
              serverUrl: "wss://test.livekit.cloud",
              participantName: "Test User",
            })
          }
        >
          Mock Join
        </button>
      </div>
    );
  };
});

jest.mock("@/components/InterviewRoom", () => {
  return function MockInterviewRoom({ roomData, onDisconnect }: any) {
    return (
      <div data-testid="interview-room">
        <div>Room: {roomData.roomName}</div>
        <div>Participant: {roomData.participantName}</div>
        <button onClick={onDisconnect}>Mock Disconnect</button>
      </div>
    );
  };
});

describe("CallPage", () => {
  it("renders the join form initially", () => {
    render(<CallPage />);

    expect(screen.getByTestId("join-form")).toBeInTheDocument();
    expect(screen.queryByTestId("interview-room")).not.toBeInTheDocument();
  });

  it("shows interview room after successful join", async () => {
    const user = userEvent.setup();
    render(<CallPage />);

    const joinButton = screen.getByText("Mock Join");
    await user.click(joinButton);

    await waitFor(() => {
      expect(screen.getByTestId("interview-room")).toBeInTheDocument();
      expect(screen.getByText("Room: test-room")).toBeInTheDocument();
      expect(screen.getByText("Participant: Test User")).toBeInTheDocument();
    });

    expect(screen.queryByTestId("join-form")).not.toBeInTheDocument();
  });

  it("returns to join form after disconnect", async () => {
    const user = userEvent.setup();
    render(<CallPage />);

    // Join first
    const joinButton = screen.getByText("Mock Join");
    await user.click(joinButton);

    await waitFor(() => {
      expect(screen.getByTestId("interview-room")).toBeInTheDocument();
    });

    // Then disconnect
    const disconnectButton = screen.getByText("Mock Disconnect");
    await user.click(disconnectButton);

    await waitFor(() => {
      expect(screen.getByTestId("join-form")).toBeInTheDocument();
      expect(screen.queryByTestId("interview-room")).not.toBeInTheDocument();
    });
  });

  it("manages room data state correctly", async () => {
    const user = userEvent.setup();
    render(<CallPage />);

    const joinButton = screen.getByText("Mock Join");
    await user.click(joinButton);

    await waitFor(() => {
      const roomElement = screen.getByTestId("interview-room");
      expect(roomElement).toBeInTheDocument();
      
      // Check that room data is passed correctly
      expect(screen.getByText("Room: test-room")).toBeInTheDocument();
      expect(screen.getByText("Participant: Test User")).toBeInTheDocument();
    });
  });
});
